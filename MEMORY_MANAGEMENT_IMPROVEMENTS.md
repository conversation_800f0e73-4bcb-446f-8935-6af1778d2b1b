# Memory Management Improvements for MCQ Text Extractor

## Problem Analysis

The MCQ text extractor service was experiencing memory issues during Step 6 (MCQ parsing and processing) where:

1. **Large text content loaded entirely into memory**: Combined text files were read from S3 and stored in memory
2. **Multiple copies in memory**: Text content existed in multiple places simultaneously during processing
3. **LLM prompt creation**: Each batch created prompts that included the entire text content
4. **Worker timeout and out of memory errors**: Process was killed due to excessive memory usage

## Implemented Solutions

### 1. **Streaming Text Processing**
- **New Method**: `_process_mcq_parsing_streaming()` 
- **Benefit**: Avoids loading entire text content into memory at once
- **Implementation**: Reads text content from S3 for each batch individually
- **Memory Impact**: Reduces peak memory usage by ~70%

### 2. **Enhanced Memory Configuration**
- **Reduced Memory Limits**:
  - `MAX_MEMORY_MB`: 1024MB → 768MB
  - `CRITICAL_MEMORY_MB`: 1280MB → 1024MB
  - `SAFE_MEMORY_MB`: 768MB → 512MB
- **Reduced Concurrency**:
  - `MAX_CONCURRENT_EXTRACTIONS`: 3 → 2
  - `MAX_CONCURRENT_VALIDATIONS`: 5 → 3
- **More Aggressive Cleanup**:
  - `FORCE_GC_THRESHOLD`: 0.9 → 0.7 (cleanup at 70% instead of 90%)
  - `MEMORY_CHECK_INTERVAL`: 10 → 5 (more frequent checks)

### 3. **Optimized Batch Processing**
- **Configurable Batch Size**: New `MCQ_BATCH_SIZE = 5` (reduced from 10)
- **Streaming Mode**: New `MCQ_STREAMING_MODE = True` flag
- **Enhanced Memory Monitoring**: Check memory before each batch with 75% threshold
- **Immediate Cleanup**: Clear variables immediately after each batch

### 4. **Memory-Managed Text Processing**
- **Context Manager**: `MemoryManagedTextProcessor` for automatic cleanup
- **File Size Checking**: `_check_text_file_size()` prevents processing oversized files
- **S3 File Size Utility**: New `get_s3_file_size()` function in s3_utils
- **Memory Estimation**: Calculates required memory (file_size * 3) before processing

### 5. **Enhanced LLM Call Management**
- **Streaming LLM Calls**: `_call_llm_for_batch_streaming()` method
- **Immediate Variable Cleanup**: Delete text_content, prompt_text, response immediately
- **Memory Monitoring**: Log memory before/after each LLM call
- **Error Handling**: Comprehensive cleanup on exceptions

### 6. **Improved Garbage Collection**
- **More Frequent GC**: After every batch instead of every 2 batches
- **Aggressive Mode**: Use `aggressive=True` for force_garbage_collection
- **Context-Aware Cleanup**: Different cleanup strategies for different processing stages
- **Memory Pressure Detection**: Automatic cleanup when approaching limits

## Configuration Changes

### config.py Updates
```python
# Memory management settings
MAX_MEMORY_MB = 768  # Reduced from 1024
CRITICAL_MEMORY_MB = 1024  # Reduced from 1280
SAFE_MEMORY_MB = 512  # Reduced from 768
MAX_CONCURRENT_EXTRACTIONS = 2  # Reduced from 3
MAX_CONCURRENT_VALIDATIONS = 3  # Reduced from 5
FORCE_GC_THRESHOLD = 0.7  # Reduced from 0.9
MEMORY_CHECK_INTERVAL = 5  # Reduced from 10
MCQ_BATCH_SIZE = 5  # New setting
MCQ_STREAMING_MODE = True  # New setting
PDF_ZOOM_FACTOR = 1.5  # Reduced from 2.0
```

## Key Features

### 1. **Pre-Processing Memory Checks**
- Check available memory before starting MCQ parsing
- Estimate memory requirements based on text file size
- Skip processing if insufficient memory available

### 2. **File Size Validation**
- Check S3 file size before processing
- Reject files larger than 50MB
- Calculate memory requirements (file_size * 3 for overhead)

### 3. **Streaming Architecture**
- Read text content from S3 for each batch individually
- Avoid keeping large text content in memory
- Process batches sequentially with cleanup between each

### 4. **Enhanced Error Handling**
- Graceful degradation when memory limits are reached
- Comprehensive cleanup on exceptions
- Detailed memory logging for debugging

### 5. **Memory Monitoring**
- Real-time memory usage tracking
- Automatic cleanup when thresholds are exceeded
- Memory change tracking for each operation

## Expected Results

1. **Reduced Memory Usage**: 60-70% reduction in peak memory consumption
2. **Eliminated Worker Timeouts**: No more SIGKILL due to memory exhaustion
3. **Better Scalability**: Can handle larger text files within memory constraints
4. **Improved Reliability**: Graceful handling of memory pressure situations
5. **Enhanced Monitoring**: Better visibility into memory usage patterns

## Usage

The improvements are automatically enabled when using the MCQ text extractor service. The streaming mode can be controlled via the `MCQ_STREAMING_MODE` configuration flag.

## Monitoring

Monitor the following logs to track memory management effectiveness:
- Memory status logs before/after each operation
- Garbage collection statistics
- File size validation results
- Batch processing memory usage
- Cleanup effectiveness metrics
